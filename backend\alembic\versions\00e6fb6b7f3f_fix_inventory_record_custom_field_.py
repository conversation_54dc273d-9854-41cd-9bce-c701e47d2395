"""fix_inventory_record_custom_field_values_cascade_delete

Revision ID: 00e6fb6b7f3f
Revises: 49423c7a53e3
Create Date: 2025-07-01 16:29:04.002002

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '00e6fb6b7f3f'
down_revision: Union[str, None] = '49423c7a53e3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 清理孤立的自定义字段值记录
    # 删除那些引用了不存在的盘点记录的自定义字段值
    op.execute("""
        DELETE FROM inventory_record_custom_field_values 
        WHERE inventory_record_id IS NOT NULL 
        AND inventory_record_id NOT IN (
            SELECT id FROM inventory_records
        )
    """)


def downgrade() -> None:
    # 降级时不需要特殊操作，因为这只是清理数据
    pass
