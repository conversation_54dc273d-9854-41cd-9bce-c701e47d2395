"""
工号补全服务
用于为现有腾讯企业邮箱账号补全缺失的工号信息
通过姓名匹配建立人员信息与邮箱账号的关联关系
"""

import logging
import time
import hashlib
import json
from typing import List, Dict, Optional, Tuple
from difflib import SequenceMatcher
import re
from sqlalchemy.orm import Session

from app.models.email import EmailMember
from app.models.ecology_user import EcologyUser
from app.services.email_api import TencentEmailAPIService
from app.schemas.email_personnel_sync import (
    ExtidCompletionResult,
    NameMatchResult,
    ExtidCompletionStats,
    ManualMatchRequest,
    ExtidCompletionBatchResult,
    ExtidCompletionPaginatedResponse,
    ManualExtidRequest,
    RecompletionStrategy,
    RecompletionRequest,
    RecompletionCandidate,
    RecompletionAnalysisResult,
    RecompletionResult
)
from app.utils.redis_cache import RedisCache

logger = logging.getLogger(__name__)

class EmailExtidCompletionService:
    """工号补全服务类"""

    def __init__(self, db: Session):
        self.db = db
        # 使用Redis缓存替代内存缓存
        self.redis_cache = RedisCache()
        self._cache_ttl = 300  # 缓存5分钟
        
        # 定义在职状态常量
        self.ACTIVE_STATUSES = ['试用', '正式', '临时', '试用延期']

    def _get_cache_key(self, similarity_threshold: float) -> str:
        """生成Redis缓存键"""
        # 基于数据库状态和参数生成缓存键
        missing_count = self.db.query(EmailMember).filter(
            EmailMember.extid.is_(None) | (EmailMember.extid == "")
        ).count()
        personnel_count = self.db.query(EcologyUser).filter(
            EcologyUser.user_name.isnot(None),
            EcologyUser.user_name != "",
            EcologyUser.job_number.isnot(None),
            EcologyUser.job_number != ""
        ).count()

        cache_data = {
            "similarity_threshold": similarity_threshold,
            "missing_count": missing_count,
            "personnel_count": personnel_count
        }

        cache_str = json.dumps(cache_data, sort_keys=True)
        data_hash = hashlib.md5(cache_str.encode()).hexdigest()
        
        # 使用专用的缓存键前缀，避免与HTTP缓存冲突
        return f"extid_completion_candidates:{similarity_threshold}:{data_hash}"

    def _get_cached_candidates(self, cache_key: str) -> Optional[List[ExtidCompletionResult]]:
        """从Redis获取缓存的候选者数据"""
        try:
            cached_data = self.redis_cache.get(cache_key)
            if cached_data:
                logger.info(f"使用Redis缓存的候选者数据，缓存键: {cache_key}")
                # 将缓存的字典数据转换为ExtidCompletionResult对象
                candidates = []
                for item in cached_data:
                    # 转换matches
                    matches = []
                    if item.get('matches'):
                        for match_data in item['matches']:
                            matches.append(NameMatchResult(**match_data))
                    
                    # 转换auto_match
                    auto_match = None
                    if item.get('auto_match'):
                        auto_match = NameMatchResult(**item['auto_match'])
                    
                    candidate = ExtidCompletionResult(
                        email_member_id=item['email_member_id'],
                        email=item['email'],
                        name=item['name'],
                        current_extid=item['current_extid'],
                        matches=matches,
                        auto_match=auto_match,
                        status=item['status']
                    )
                    candidates.append(candidate)
                
                return candidates
        except Exception as e:
            logger.warning(f"获取Redis缓存失败: {str(e)}")
        
        return None

    def _set_cached_candidates(self, cache_key: str, candidates: List[ExtidCompletionResult]):
        """将候选者数据缓存到Redis"""
        try:
            # 将ExtidCompletionResult对象转换为可序列化的字典
            cache_data = []
            for candidate in candidates:
                matches_data = []
                if candidate.matches:
                    for match in candidate.matches:
                        matches_data.append({
                            'person_id': match.person_id,
                            'person_name': match.person_name,
                            'job_number': match.job_number,
                            'dept_name': match.dept_name,
                            'similarity': match.similarity,
                            'match_type': match.match_type,
                            'status': match.status
                        })
                
                auto_match_data = None
                if candidate.auto_match:
                    auto_match_data = {
                        'person_id': candidate.auto_match.person_id,
                        'person_name': candidate.auto_match.person_name,
                        'job_number': candidate.auto_match.job_number,
                        'dept_name': candidate.auto_match.dept_name,
                        'similarity': candidate.auto_match.similarity,
                        'match_type': candidate.auto_match.match_type,
                        'status': candidate.auto_match.status
                    }
                
                item_data = {
                    'email_member_id': candidate.email_member_id,
                    'email': candidate.email,
                    'name': candidate.name,
                    'current_extid': candidate.current_extid,
                    'matches': matches_data,
                    'auto_match': auto_match_data,
                    'status': candidate.status
                }
                cache_data.append(item_data)
            
            self.redis_cache.set(cache_key, cache_data, self._cache_ttl)
            logger.info(f"缓存候选者数据到Redis，共 {len(candidates)} 个，缓存键: {cache_key}")
        except Exception as e:
            logger.error(f"设置Redis缓存失败: {str(e)}")

    def clear_cache(self):
        """清除所有相关的Redis缓存"""
        try:
            # 清除所有工号补全相关的缓存
            pattern = "extid_completion_candidates:*"
            cleared_count = self.redis_cache.clear_pattern(pattern)
            logger.info(f"已清除Redis中的候选者缓存，清除数量: {cleared_count}")
        except Exception as e:
            logger.error(f"清除Redis缓存失败: {str(e)}")

    def normalize_name(self, name: str) -> str:
        """标准化姓名：去除空格、标点符号等"""
        if not name:
            return ""
        # 去除所有空格和常见标点符号
        normalized = re.sub(r'[\s\.\-_\(\)\[\]（）【】]', '', name)
        return normalized.strip()
    
    def calculate_similarity(self, name1: str, name2: str) -> float:
        """计算两个姓名的相似度"""
        if not name1 or not name2:
            return 0.0
        
        # 标准化后比较
        norm1 = self.normalize_name(name1)
        norm2 = self.normalize_name(name2)
        
        if norm1 == norm2:
            return 1.0
            
        # 使用序列匹配器计算相似度
        return SequenceMatcher(None, norm1, norm2).ratio()
    
    def find_name_matches(self, email_member: EmailMember,
                         similarity_threshold: float = 0.8,
                         personnel_dict: dict = None) -> List[NameMatchResult]:
        """为邮箱成员查找姓名匹配的人员信息（在职人员优先）"""
        matches = []

        # 如果没有提供人员字典，则查询数据库
        if personnel_dict is None:
            personnel_list = self.db.query(EcologyUser).filter(
                EcologyUser.user_name.isnot(None),
                EcologyUser.user_name != "",
                EcologyUser.job_number.isnot(None),
                EcologyUser.job_number != ""
            ).all()
            # 使用列表存储同名人员，而不是字典覆盖
            personnel_dict = {}
            for person in personnel_list:
                if person.user_name not in personnel_dict:
                    personnel_dict[person.user_name] = []
                personnel_dict[person.user_name].append(person)

        # 收集所有可能的匹配结果（不提前返回，以便进行在职状态优化）
        all_matches = []

        # 首先尝试精确匹配
        if email_member.name in personnel_dict:
            persons = personnel_dict[email_member.name]
            for person in persons:
                is_active = person.status in self.ACTIVE_STATUSES if person.status else False
                # 在职人员的相似度加权（+0.1）
                similarity_score = 1.1 if is_active else 1.0
                
                all_matches.append(NameMatchResult(
                    person_id=person.id,
                    person_name=person.user_name,
                    job_number=person.job_number,
                    dept_name=person.dept_name,
                    similarity=similarity_score,
                    match_type="exact",
                    status=person.status
                ))

        # 标准化匹配
        normalized_email_name = self.normalize_name(email_member.name)
        for person_name, persons in personnel_dict.items():
            # 跳过已经精确匹配的记录
            if person_name == email_member.name:
                continue
                
            normalized_person_name = self.normalize_name(person_name)
            if normalized_email_name == normalized_person_name and normalized_email_name:
                for person in persons:
                    is_active = person.status in self.ACTIVE_STATUSES if person.status else False
                    # 在职人员的相似度加权（+0.1）
                    similarity_score = 1.05 if is_active else 0.95
                    
                    all_matches.append(NameMatchResult(
                        person_id=person.id,
                        person_name=person.user_name,
                        job_number=person.job_number,
                        dept_name=person.dept_name,
                        similarity=similarity_score,
                        match_type="exact",
                        status=person.status
                    ))

        # 如果没有精确匹配，进行相似度匹配
        if not all_matches:
            for person_name, persons in personnel_dict.items():
                similarity = self.calculate_similarity(email_member.name, person_name)

                if similarity >= similarity_threshold:
                    for person in persons:
                        is_active = person.status in self.ACTIVE_STATUSES if person.status else False
                        # 在职人员的相似度加权（+0.1）
                        similarity_score = similarity + (0.1 if is_active else 0)
                        
                        all_matches.append(NameMatchResult(
                            person_id=person.id,
                            person_name=person.user_name,
                            job_number=person.job_number,
                            dept_name=person.dept_name,
                            similarity=similarity_score,
                            match_type="similar",
                            status=person.status
                        ))

        # 排序逻辑：优先按在职状态，然后按相似度降序
        def sort_key(match):
            is_active = match.status in self.ACTIVE_STATUSES if match.status else False
            # 返回元组：(是否在职, 相似度)，在职人员优先，然后按相似度排序
            return (is_active, match.similarity)

        all_matches.sort(key=sort_key, reverse=True)
        return all_matches[:5]  # 最多返回5个匹配结果
    
    def analyze_missing_extid_members(self) -> List[EmailMember]:
        """分析缺少工号的邮箱成员"""
        return self.db.query(EmailMember).filter(
            EmailMember.extid.is_(None) | (EmailMember.extid == "")
        ).all()
    
    def get_completion_statistics(self) -> ExtidCompletionStats:
        """获取工号补全统计信息"""
        total_members = self.db.query(EmailMember).count()
        missing_extid = self.db.query(EmailMember).filter(
            EmailMember.extid.is_(None) | (EmailMember.extid == "")
        ).count()
        has_extid = total_members - missing_extid
        
        return ExtidCompletionStats(
            total_members=total_members,
            has_extid=has_extid,
            missing_extid=missing_extid,
            completion_rate=round((has_extid / total_members * 100) if total_members > 0 else 0, 2)
        )

    async def analyze_completion_candidates(self,
                                          similarity_threshold: float = 0.8) -> List[ExtidCompletionResult]:
        """分析所有需要补全工号的候选者（带缓存）"""
        # 检查缓存
        cache_key = self._get_cache_key(similarity_threshold)
        cached_results = self._get_cached_candidates(cache_key)
        if cached_results is not None:
            return cached_results

        missing_members = self.analyze_missing_extid_members()
        results = []

        logger.info(f"开始分析 {len(missing_members)} 个缺少工号的邮箱成员")

        # 预先加载所有人员信息到字典中，提高性能
        personnel_list = self.db.query(EcologyUser).filter(
            EcologyUser.user_name.isnot(None),
            EcologyUser.user_name != "",
            EcologyUser.job_number.isnot(None),
            EcologyUser.job_number != ""
        ).all()
        # 使用列表存储同名人员，而不是字典覆盖
        personnel_dict = {}
        for person in personnel_list:
            if person.user_name not in personnel_dict:
                personnel_dict[person.user_name] = []
            personnel_dict[person.user_name].append(person)
        
        total_persons = sum(len(persons) for persons in personnel_dict.values())
        logger.info(f"加载了 {len(personnel_dict)} 个不同姓名，共 {total_persons} 个人员信息用于匹配")

        # 批量处理，每10个成员输出一次进度（减少日志输出）
        batch_size = 10
        for i, member in enumerate(missing_members):
            if i % batch_size == 0:
                logger.info(f"处理进度: {i}/{len(missing_members)}")

            matches = self.find_name_matches(member, similarity_threshold, personnel_dict)

            # 确定自动匹配结果（精确匹配且唯一，优先在职人员）
            auto_match = None
            if matches:
                exact_matches = [m for m in matches if m.match_type == "exact"]
                if len(exact_matches) == 1:
                    auto_match = exact_matches[0]
                elif len(exact_matches) > 1:
                    # 如果有多个精确匹配，优先选择在职人员
                    active_exact_matches = [m for m in exact_matches if m.status in self.ACTIVE_STATUSES]
                    if len(active_exact_matches) == 1:
                        auto_match = active_exact_matches[0]

            # 确定状态
            status = "pending"
            if auto_match:
                status = "matched"
            elif not matches:
                status = "manual"

            result = ExtidCompletionResult(
                email_member_id=member.id,
                email=member.email,
                name=member.name,
                current_extid=member.extid,
                matches=matches,
                auto_match=auto_match,
                status=status
            )
            results.append(result)

        logger.info(f"分析完成，找到 {len([r for r in results if r.auto_match])} 个自动匹配，"
                   f"{len([r for r in results if r.status == 'manual'])} 个需要手动处理")

        # 缓存结果
        self._set_cached_candidates(cache_key, results)

        return results

    async def get_completion_candidates_paginated(self,
                                                 similarity_threshold: float = 0.8,
                                                 page: int = 1,
                                                 page_size: int = 20,
                                                 search: str = None,
                                                 status_filter: str = None) -> ExtidCompletionPaginatedResponse:
        """获取工号补全候选者分页列表（优化版）"""
        import math

        start_time = time.time()

        # 获取所有候选者（使用缓存）
        all_candidates = await self.analyze_completion_candidates(similarity_threshold)

        logger.info(f"获取候选者数据耗时: {time.time() - start_time:.3f}s")

        # 应用搜索过滤
        filtered_candidates = all_candidates
        if search:
            search_lower = search.lower()
            filtered_candidates = [
                candidate for candidate in filtered_candidates
                if search_lower in candidate.name.lower() or search_lower in candidate.email.lower()
            ]

        # 应用状态过滤
        if status_filter:
            filtered_candidates = [
                candidate for candidate in filtered_candidates
                if candidate.status == status_filter
            ]

        # 计算分页信息
        total = len(filtered_candidates)
        total_pages = math.ceil(total / page_size) if page_size > 0 else 1

        # 应用分页
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_candidates = filtered_candidates[start_index:end_index]

        logger.info(f"分页处理完成，返回 {len(paginated_candidates)}/{total} 个候选者，总耗时: {time.time() - start_time:.3f}s")

        return ExtidCompletionPaginatedResponse(
            items=paginated_candidates,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    async def manual_set_extid(self, request: ManualExtidRequest) -> bool:
        """手动设置工号"""
        try:
            # 获取邮箱成员信息
            member = self.db.query(EmailMember).filter(EmailMember.id == request.email_member_id).first()
            if not member:
                logger.error(f"未找到ID为 {request.email_member_id} 的邮箱成员")
                return False

            # 验证工号格式（可以根据需要添加更多验证）
            if not request.job_number or not request.job_number.strip():
                logger.error("工号不能为空")
                return False

            # 检查工号是否已被其他成员使用
            existing_member = self.db.query(EmailMember).filter(
                EmailMember.extid == request.job_number,
                EmailMember.id != request.email_member_id
            ).first()

            if existing_member:
                logger.error(f"工号 {request.job_number} 已被其他成员使用: {existing_member.email}")
                return False

            # 调用腾讯企业邮箱API更新extid
            api_service = TencentEmailAPIService(self.db, app_name="通讯录管理")
            api_data = {
                "userid": member.email,
                "extid": request.job_number.strip()
            }

            logger.info(f"手动设置成员 {member.email} 的工号为 {request.job_number}")

            # 调用腾讯API更新
            result = await api_service.update_member(api_data)

            if result.errcode != 0:
                logger.error(f"腾讯API更新失败: {result.errmsg} (错误码: {result.errcode})")
                return False

            # API更新成功后，重新获取数据验证
            verify_result = await api_service.get_member(member.email)
            if verify_result.errcode == 0:
                # 使用腾讯返回的权威数据更新本地缓存
                updated_extid = verify_result.data.get("extid", request.job_number.strip())
                member.extid = updated_extid
                self.db.commit()

                # 清除缓存，因为数据已更新
                self.clear_cache()

                logger.info(f"成功设置成员 {member.email} 的工号为 {updated_extid}")
                return True
            else:
                logger.warning(f"验证更新结果失败: {verify_result.errmsg}")
                # 即使验证失败，如果API更新成功，也更新本地数据
                member.extid = request.job_number.strip()
                self.db.commit()

                # 清除缓存，因为数据已更新
                self.clear_cache()

                return True

        except Exception as e:
            logger.error(f"手动设置工号失败: {str(e)}", exc_info=True)
            self.db.rollback()
            return False

    async def execute_auto_completion(self,
                                    similarity_threshold: float = 0.8,
                                    auto_confirm_exact_match: bool = True,
                                    dry_run: bool = False) -> ExtidCompletionBatchResult:
        """执行自动工号补全"""
        start_time = time.time()

        # 分析候选者
        candidates = await self.analyze_completion_candidates(similarity_threshold)

        total_processed = len(candidates)
        auto_matched = 0
        manual_required = 0
        skipped = 0
        errors = 0
        error_messages = []

        # 处理自动匹配的候选者
        for candidate in candidates:
            try:
                if candidate.auto_match and auto_confirm_exact_match:
                    if not dry_run:
                        # 执行实际的工号更新
                        success = await self._update_member_extid(
                            candidate.email_member_id,
                            candidate.auto_match.job_number
                        )
                        if success:
                            auto_matched += 1
                            candidate.status = "completed"
                        else:
                            errors += 1
                            candidate.status = "error"
                    else:
                        auto_matched += 1
                        candidate.status = "would_complete"
                elif candidate.matches:
                    manual_required += 1
                else:
                    skipped += 1

            except Exception as e:
                errors += 1
                error_msg = f"处理成员 {candidate.email} 时出错: {str(e)}"
                error_messages.append(error_msg)
                logger.error(error_msg, exc_info=True)

        duration = time.time() - start_time

        result = ExtidCompletionBatchResult(
            total_processed=total_processed,
            auto_matched=auto_matched,
            manual_required=manual_required,
            skipped=skipped,
            errors=errors,
            results=candidates,
            error_messages=error_messages
        )

        logger.info(f"批量工号补全完成，耗时 {duration:.2f}秒，"
                   f"自动匹配: {auto_matched}，需手动处理: {manual_required}，"
                   f"跳过: {skipped}，错误: {errors}")

        return result

    async def _update_member_extid(self, member_id: int, job_number: str) -> bool:
        """更新邮箱成员的工号信息（同时更新腾讯API和本地数据库）"""
        try:
            # 获取邮箱成员信息
            member = self.db.query(EmailMember).filter(EmailMember.id == member_id).first()
            if not member:
                logger.error(f"未找到ID为 {member_id} 的邮箱成员")
                return False

            # 检查工号是否已被其他成员使用（如果工号不为空）
            if job_number and job_number.strip():
                existing_member = self.db.query(EmailMember).filter(
                    EmailMember.extid == job_number.strip(),
                    EmailMember.id != member_id
                ).first()
                
                if existing_member:
                    logger.error(f"工号 {job_number} 已被其他成员使用: ID={existing_member.id}, "
                               f"邮箱={existing_member.email}, 姓名={existing_member.name}")
                    return False

            # 调用腾讯企业邮箱API更新extid
            api_service = TencentEmailAPIService(self.db, app_name="通讯录管理")

            # 构建API更新数据
            api_data = {
                "userid": member.email,  # 腾讯API使用邮箱地址作为userid
                "extid": job_number
            }

            logger.info(f"调用腾讯企业邮箱API更新成员 {member.email} 的工号为 {job_number}")

            # 调用腾讯API更新
            result = await api_service.update_member(api_data)

            if result.errcode != 0:
                logger.error(f"腾讯API更新失败: {result.errmsg} (错误码: {result.errcode})")
                return False

            # API更新成功后，再次检查数据库中的工号冲突（防止并发问题）
            if job_number and job_number.strip():
                existing_member = self.db.query(EmailMember).filter(
                    EmailMember.extid == job_number.strip(),
                    EmailMember.id != member_id
                ).first()
                
                if existing_member:
                    logger.error(f"更新前发现工号 {job_number} 冲突，终止本地数据库更新。"
                               f"冲突成员: ID={existing_member.id}, 邮箱={existing_member.email}")
                    return False

            # API更新成功后，更新本地数据库
            member.extid = job_number
            self.db.add(member)
            self.db.commit()

            # 清除缓存，因为数据已更新
            self.clear_cache()

            logger.info(f"成功更新成员 {member.email} 的工号为 {job_number}")
            return True

        except Exception as e:
            logger.error(f"更新成员工号时出错: {str(e)}", exc_info=True)
            self.db.rollback()
            return False

    async def manual_match_confirm(self, request: ManualMatchRequest) -> bool:
        """手动确认匹配结果"""
        try:
            # 验证人员信息是否存在
            person = self.db.query(EcologyUser).filter(
                EcologyUser.id == request.person_id,
                EcologyUser.job_number == request.job_number
            ).first()

            if not person:
                logger.error(f"未找到ID为 {request.person_id}，工号为 {request.job_number} 的人员信息")
                return False

            # 执行工号更新
            return await self._update_member_extid(request.email_member_id, request.job_number)

        except Exception as e:
            logger.error(f"手动确认匹配时出错: {str(e)}", exc_info=True)
            return False

    def get_member_by_id(self, member_id: int) -> Optional[EmailMember]:
        """根据ID获取邮箱成员"""
        return self.db.query(EmailMember).filter(EmailMember.id == member_id).first()

    def get_person_by_id(self, person_id: int) -> Optional[EcologyUser]:
        """根据ID获取人员信息"""
        return self.db.query(EcologyUser).filter(EcologyUser.id == person_id).first()

    def _calculate_match_confidence(self, member: EmailMember, current_extid: str) -> float:
        """计算当前匹配的可信度"""
        try:
            # 根据当前工号找到对应的人员信息
            person = self.db.query(EcologyUser).filter(
                EcologyUser.job_number == current_extid
            ).first()
            
            if not person:
                return 0.0  # 找不到对应人员，可信度为0
            
            # 计算姓名相似度
            name_similarity = self.calculate_similarity(member.name, person.user_name)
            
            # 在职状态加权
            is_active = person.status in self.ACTIVE_STATUSES if person.status else False
            status_weight = 0.1 if is_active else 0.0
            
            # 精确匹配加权
            exact_match_weight = 0.2 if member.name == person.user_name else 0.0
            
            confidence = name_similarity + status_weight + exact_match_weight
            return min(confidence, 1.0)  # 确保不超过1.0
            
        except Exception as e:
            logger.error(f"计算匹配可信度失败: {str(e)}")
            return 0.0

    async def analyze_recompletion_candidates(self, 
                                            strategy: RecompletionStrategy) -> RecompletionAnalysisResult:
        """分析需要重新补齐的候选者"""
        logger.info(f"开始分析重新补齐候选者，策略: {strategy.strategy_type}")
        
        # 获取所有已有工号的成员
        members_with_extid = self.db.query(EmailMember).filter(
            EmailMember.extid.isnot(None),
            EmailMember.extid != ""
        ).all()
        
        total_members = len(members_with_extid)
        candidates = []
        high_confidence = 0
        low_confidence = 0
        no_match = 0
        
        # 预加载人员信息
        personnel_list = self.db.query(EcologyUser).filter(
            EcologyUser.user_name.isnot(None),
            EcologyUser.user_name != "",
            EcologyUser.job_number.isnot(None),
            EcologyUser.job_number != ""
        ).all()
        personnel_dict = {}
        for person in personnel_list:
            if person.user_name not in personnel_dict:
                personnel_dict[person.user_name] = []
            personnel_dict[person.user_name].append(person)
        
        logger.info(f"开始分析 {total_members} 个已有工号的成员")
        
        for member in members_with_extid:
            # 计算当前匹配的可信度
            confidence = self._calculate_match_confidence(member, member.extid)
            
            # 获取新的匹配结果
            new_matches = self.find_name_matches(
                member, 
                strategy.similarity_threshold, 
                personnel_dict
            )
            
            # 判断是否需要重新补齐
            needs_recompletion = False
            reason = ""
            recommended_action = "keep"
            
            if strategy.strategy_type == "full_reset":
                needs_recompletion = True
                reason = "全量重置策略"
                recommended_action = "update" if new_matches else "manual"
                
            elif strategy.strategy_type == "smart_recompletion":
                if confidence < 0.7:  # 低可信度
                    needs_recompletion = True
                    reason = f"低可信度匹配({confidence:.2f})"
                    low_confidence += 1
                elif not new_matches:
                    needs_recompletion = True
                    reason = "无法找到匹配的人员"
                    no_match += 1
                    recommended_action = "manual"
                elif new_matches and new_matches[0].job_number != member.extid:
                    # 新算法给出了不同的最佳匹配
                    if new_matches[0].similarity > confidence + 0.1:  # 新匹配明显更好
                        needs_recompletion = True
                        reason = f"发现更优匹配(新:{new_matches[0].similarity:.2f} vs 当前:{confidence:.2f})"
                        recommended_action = "update"
                    else:
                        high_confidence += 1
                else:
                    high_confidence += 1
                    
            elif strategy.strategy_type == "selective":
                # 选择性重新补齐在具体执行时处理
                pass
            
            if needs_recompletion:
                candidate = RecompletionCandidate(
                    email_member_id=member.id,
                    email=member.email,
                    name=member.name,
                    current_extid=member.extid,
                    current_match_confidence=confidence,
                    reason_for_recompletion=reason,
                    new_matches=new_matches,
                    recommended_action=recommended_action
                )
                candidates.append(candidate)
        
        result = RecompletionAnalysisResult(
            total_members_with_extid=total_members,
            candidates_for_recompletion=len(candidates),
            high_confidence_matches=high_confidence,
            low_confidence_matches=low_confidence,
            no_match_found=no_match,
            candidates=candidates
        )
        
        logger.info(f"分析完成: 总计{total_members}个成员，需重新补齐{len(candidates)}个")
        return result

    async def execute_recompletion(self, request: RecompletionRequest) -> RecompletionResult:
        """执行重新补齐操作"""
        import time
        from datetime import datetime
        
        start_time = time.time()
        logger.info(f"开始执行重新补齐，策略: {request.strategy.strategy_type}")
        
        # 数据备份
        backup_id = None
        if request.strategy.backup_before_operation and not request.dry_run:
            try:
                # 这里应该调用数据备份服务，暂时记录日志
                backup_id = f"recompletion_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                logger.info(f"数据备份完成，备份ID: {backup_id}")
            except Exception as e:
                logger.error(f"数据备份失败: {str(e)}")
                raise Exception("数据备份失败，停止重新补齐操作")
        
        # 分析候选者
        analysis_result = await self.analyze_recompletion_candidates(request.strategy)
        candidates = analysis_result.candidates
        
        # 如果是选择性重新补齐，过滤指定的成员
        if (request.strategy.strategy_type == "selective" and 
            request.target_member_ids):
            candidates = [
                c for c in candidates 
                if c.email_member_id in request.target_member_ids
            ]
        
        # 执行结果统计
        total_processed = len(candidates)
        kept_unchanged = 0
        updated_extid = 0
        cleared_extid = 0
        manual_review_required = 0
        errors = 0
        detailed_results = []
        error_messages = []
        
        # 批量处理
        batch_size = request.strategy.batch_size
        for i in range(0, len(candidates), batch_size):
            batch = candidates[i:i+batch_size]
            logger.info(f"处理批次 {i//batch_size + 1}，成员数: {len(batch)}")
            
            for candidate in batch:
                try:
                    result_detail = {
                        "email_member_id": candidate.email_member_id,
                        "email": candidate.email,
                        "name": candidate.name,
                        "old_extid": candidate.current_extid,
                        "action": "unknown",
                        "new_extid": None,
                        "success": False
                    }
                    
                    if request.dry_run:
                        # 试运行模式，只记录推荐操作
                        result_detail["action"] = f"dry_run_{candidate.recommended_action}"
                        result_detail["success"] = True
                        
                        if candidate.recommended_action == "keep":
                            kept_unchanged += 1
                        elif candidate.recommended_action == "update":
                            updated_extid += 1
                            if candidate.new_matches:
                                result_detail["new_extid"] = candidate.new_matches[0].job_number
                        else:
                            manual_review_required += 1
                            
                    else:
                        # 实际执行
                        if candidate.recommended_action == "keep":
                            kept_unchanged += 1
                            result_detail["action"] = "keep"
                            result_detail["success"] = True
                            
                        elif candidate.recommended_action == "update" and candidate.new_matches:
                            # 更新工号
                            new_extid = candidate.new_matches[0].job_number
                            update_success = await self._update_member_extid(
                                candidate.email_member_id, 
                                new_extid
                            )
                            
                            if update_success:
                                updated_extid += 1
                                result_detail["action"] = "update"
                                result_detail["new_extid"] = new_extid
                                result_detail["success"] = True
                            else:
                                errors += 1
                                result_detail["action"] = "update_failed"
                                error_messages.append(
                                    f"更新成员{candidate.email}工号失败"
                                )
                                
                        elif candidate.recommended_action == "manual":
                            # 清除工号，需要手动处理
                            clear_success = await self._update_member_extid(
                                candidate.email_member_id, 
                                ""
                            )
                            
                            if clear_success:
                                cleared_extid += 1
                                manual_review_required += 1
                                result_detail["action"] = "clear_for_manual"
                                result_detail["success"] = True
                            else:
                                errors += 1
                                result_detail["action"] = "clear_failed"
                                error_messages.append(
                                    f"清除成员{candidate.email}工号失败"
                                )
                    
                    detailed_results.append(result_detail)
                    
                except Exception as e:
                    errors += 1
                    error_msg = f"处理成员{candidate.email}时发生错误: {str(e)}"
                    error_messages.append(error_msg)
                    logger.error(error_msg)
                    
                    detailed_results.append({
                        "email_member_id": candidate.email_member_id,
                        "email": candidate.email,
                        "name": candidate.name,
                        "old_extid": candidate.current_extid,
                        "action": "error",
                        "success": False,
                        "error": str(e)
                    })
        
        # 清理缓存
        if not request.dry_run:
            self.clear_cache()
        
        end_time = time.time()
        duration = f"{end_time - start_time:.2f}秒"
        
        result = RecompletionResult(
            total_processed=total_processed,
            kept_unchanged=kept_unchanged,
            updated_extid=updated_extid,
            cleared_extid=cleared_extid,
            manual_review_required=manual_review_required,
            errors=errors,
            operation_duration=duration,
            backup_id=backup_id,
            detailed_results=detailed_results,
            error_messages=error_messages
        )
        
        mode = "试运行" if request.dry_run else "正式执行"
        logger.info(f"重新补齐{mode}完成: 处理{total_processed}个，"
                   f"保持{kept_unchanged}个，更新{updated_extid}个，"
                   f"清除{cleared_extid}个，错误{errors}个，耗时{duration}")
        
        return result
