"""add_timeout_to_email_sync_locks

Revision ID: 350233421c24
Revises: 8fcf61e0cabb
Create Date: 2025-07-07 08:36:03.326178

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '350233421c24'
down_revision: Union[str, None] = '8fcf61e0cabb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('email_sync_locks', sa.Column('timeout_seconds', sa.Integer(), nullable=True, server_default='1800'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('email_sync_locks', 'timeout_seconds')
    # ### end Alembic commands ###
