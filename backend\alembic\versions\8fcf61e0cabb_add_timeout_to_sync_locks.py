"""add_timeout_to_sync_locks

Revision ID: 8fcf61e0cabb
Revises: 00e6fb6b7f3f
Create Date: 2025-07-07 08:34:47.235782

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8fcf61e0cabb'
down_revision: Union[str, None] = '00e6fb6b7f3f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ad_sync_locks', sa.Column('timeout_seconds', sa.Integer(), nullable=True, server_default='1800'))
    op.alter_column('inventory_records', 'new_location',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=200),
               existing_comment='新存放位置',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('inventory_records', 'new_location',
               existing_type=sa.String(length=200),
               type_=sa.VARCHAR(length=100),
               existing_comment='新存放位置',
               existing_nullable=True)
    op.drop_column('ad_sync_locks', 'timeout_seconds')
    # ### end Alembic commands ###
