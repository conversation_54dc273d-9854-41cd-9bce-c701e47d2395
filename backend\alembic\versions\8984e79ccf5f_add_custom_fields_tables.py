"""add_custom_fields_tables

Revision ID: 8984e79ccf5f
Revises: 3cd0d15e8c93
Create Date: 2025-06-30 17:45:50.214939

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '8984e79ccf5f'
down_revision: Union[str, None] = '3cd0d15e8c93'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_personnel_sync_locks_lock_name', table_name='personnel_sync_locks')
    op.drop_table('personnel_sync_locks')
    op.alter_column('ad_sync_locks', 'locked_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.drop_column('ad_sync_locks', 'status')
    op.drop_column('ad_sync_locks', 'operation_type')
    op.alter_column('ad_sync_logs', 'operator',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=100),
               nullable=True,
               existing_comment='操作员用户名')
    op.alter_column('email_sync_locks', 'locked_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.drop_column('email_sync_locks', 'status')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('email_sync_locks', sa.Column('status', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.alter_column('email_sync_locks', 'locked_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True)
    op.alter_column('ad_sync_logs', 'operator',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=255),
               nullable=False,
               existing_comment='操作员用户名')
    op.add_column('ad_sync_locks', sa.Column('operation_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.add_column('ad_sync_locks', sa.Column('status', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.alter_column('ad_sync_locks', 'locked_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True)
    op.create_table('personnel_sync_locks',
    sa.Column('lock_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('is_locked', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('locked_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('locked_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('operation_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('lock_name', name='personnel_sync_locks_pkey')
    )
    op.create_index('ix_personnel_sync_locks_lock_name', 'personnel_sync_locks', ['lock_name'], unique=False)
    # ### end Alembic commands ###
