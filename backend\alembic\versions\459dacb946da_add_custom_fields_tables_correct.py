"""add_custom_fields_tables_correct

Revision ID: 459dacb946da
Revises: 8984e79ccf5f
Create Date: 2025-06-30 17:49:46.281801

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '459dacb946da'
down_revision: Union[str, None] = '8984e79ccf5f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('custom_fields',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='字段名称'),
    sa.Column('label', sa.String(length=200), nullable=False, comment='字段显示标签'),
    sa.Column('field_type', sa.String(length=50), nullable=False, comment='字段类型'),
    sa.Column('description', sa.Text(), nullable=True, comment='字段描述'),
    sa.Column('is_required', sa.Boolean(), nullable=False, comment='是否必填'),
    sa.Column('default_value', sa.Text(), nullable=True, comment='默认值'),
    sa.Column('options', sa.JSON(), nullable=True, comment='字段选项配置'),
    sa.Column('validation_rules', sa.JSON(), nullable=True, comment='验证规则'),
    sa.Column('sort_order', sa.Integer(), nullable=False, comment='排序顺序'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
    sa.Column('applies_to', sa.String(length=50), nullable=False, comment='适用范围'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_custom_fields_id'), 'custom_fields', ['id'], unique=False)
    op.create_table('asset_custom_field_values',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('asset_id', sa.Integer(), nullable=False, comment='资产ID'),
    sa.Column('custom_field_id', sa.Integer(), nullable=False, comment='自定义字段ID'),
    sa.Column('value', sa.Text(), nullable=True, comment='字段值'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['asset_id'], ['assets.id'], ),
    sa.ForeignKeyConstraint(['custom_field_id'], ['custom_fields.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_asset_custom_field_values_id'), 'asset_custom_field_values', ['id'], unique=False)
    op.create_table('inventory_record_custom_field_values',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('inventory_record_id', sa.Integer(), nullable=True, comment='盘点记录ID'),
    sa.Column('task_id', sa.Integer(), nullable=True, comment='盘点任务ID'),
    sa.Column('asset_id', sa.Integer(), nullable=True, comment='资产ID'),
    sa.Column('custom_field_id', sa.Integer(), nullable=False, comment='自定义字段ID'),
    sa.Column('value', sa.Text(), nullable=True, comment='字段值'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['asset_id'], ['assets.id'], ),
    sa.ForeignKeyConstraint(['custom_field_id'], ['custom_fields.id'], ),
    sa.ForeignKeyConstraint(['inventory_record_id'], ['inventory_records.id'], ),
    sa.ForeignKeyConstraint(['task_id'], ['inventory_tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_inventory_record_custom_field_values_id'), 'inventory_record_custom_field_values', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_inventory_record_custom_field_values_id'), table_name='inventory_record_custom_field_values')
    op.drop_table('inventory_record_custom_field_values')
    op.drop_index(op.f('ix_asset_custom_field_values_id'), table_name='asset_custom_field_values')
    op.drop_table('asset_custom_field_values')
    op.drop_index(op.f('ix_custom_fields_id'), table_name='custom_fields')
    op.drop_table('custom_fields')
    # ### end Alembic commands ###
